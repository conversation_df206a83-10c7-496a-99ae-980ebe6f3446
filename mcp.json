{"name": "openweather-mcp-server", "version": "1.0.0", "description": "OpenWeatherMap MCP Server - Gerçek zamanlı hava durumu verileri sa<PERSON>", "author": "Your Name", "license": "MIT", "homepage": "https://github.com/yourusername/openweather-mcp-server", "repository": {"type": "git", "url": "https://github.com/yourusername/openweather-mcp-server.git"}, "mcp": {"server": {"name": "OpenWeather MCP Server", "version": "1.0.0", "description": "OpenWeatherMap API kullanarak detaylı hava durumu bilgileri sağlar. Latitude ve longitude koordinatları ile sıcaklık, nem, r<PERSON><PERSON><PERSON>, basınç ve diğer meteorolojik verileri alabilirsiniz.", "instructions": "Bu MCP server ile hava durumu bilgilerini almak için getWeather tool'unu kullanın. Latitude (-90 ile 90 arası) ve longitude (-180 ile 180 arası) parametreleri gereklidir.", "capabilities": {"tools": true, "resources": false, "prompts": false}, "tools": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Belirtilen koordinatlarda detaylı hava durumu bilgilerini OpenWeatherMap API'den alır", "parameters": {"type": "object", "properties": {"latitude": {"type": "number", "minimum": -90, "maximum": 90, "description": "Enlem koordinatı (-90 ile 90 arası)"}, "longitude": {"type": "number", "minimum": -180, "maximum": 180, "description": "<PERSON>lam koordinatı (-180 ile 180 arası)"}}, "required": ["latitude", "longitude"]}, "examples": [{"description": "İstanbul hava durumu", "parameters": {"latitude": 41.0082, "longitude": 28.9784}}, {"description": "Ankara hava durumu", "parameters": {"latitude": 39.9334, "longitude": 32.8597}}, {"description": "New York hava durumu", "parameters": {"latitude": 40.7128, "longitude": -74.006}}]}], "environment": {"node": ">=18.0.0", "runtime": "node"}, "dependencies": {"fastmcp": "^1.21.0", "node-fetch": "^3.3.2", "zod": "^3.24.2"}}}, "tags": ["weather", "openweathermap", "api", "climate", "meteorology", "temperature", "humidity", "wind", "pressure"], "categories": ["data", "api", "weather"]}